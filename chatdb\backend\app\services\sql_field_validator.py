"""
SQL字段验证模块
验证生成的SQL是否符合字段优先级规则
"""

import re
from typing import Dict, List, Any, Tuple
from app.services.query_intent_analyzer import query_intent_analyzer

class SQLFieldValidator:
    """SQL字段验证器"""
    
    def __init__(self):
        self.field_conflict_patterns = {
            # 科目字段冲突模式
            "科目字段冲突": {
                "conflicting_fields": ["account_code", "account_full_name", "account_name"],
                "preferred_field": "account_full_name",
                "conflict_description": "同时使用了多个科目字段"
            },
            
            # 公司字段冲突模式
            "公司字段冲突": {
                "conflicting_fields": ["accounting_unit_name", "accounting_organization", "company_name"],
                "preferred_field": "accounting_unit_name", 
                "conflict_description": "同时使用了多个公司字段"
            }
        }
    
    def validate_sql_field_usage(self, sql: str, query: str) -> Dict[str, Any]:
        """
        验证SQL中的字段使用是否符合优先级规则
        
        Args:
            sql: 生成的SQL语句
            query: 原始用户查询
            
        Returns:
            Dict: 验证结果
        """
        validation_result = {
            "is_valid": True,
            "violations": [],
            "suggestions": [],
            "corrected_sql": sql
        }
        
        # 1. 检查字段冲突
        conflicts = self._detect_field_conflicts(sql)
        if conflicts:
            validation_result["is_valid"] = False
            validation_result["violations"].extend(conflicts)
        
        # 2. 检查字段优先级违规
        priority_violations = self._check_field_priority_violations(sql, query)
        if priority_violations:
            validation_result["is_valid"] = False
            validation_result["violations"].extend(priority_violations)
        
        # 3. 生成修正建议
        if not validation_result["is_valid"]:
            suggestions = self._generate_correction_suggestions(sql, query, validation_result["violations"])
            validation_result["suggestions"] = suggestions
            
            # 4. 尝试自动修正
            corrected_sql = self._auto_correct_sql(sql, query, validation_result["violations"])
            validation_result["corrected_sql"] = corrected_sql
        
        return validation_result
    
    def _detect_field_conflicts(self, sql: str) -> List[Dict[str, Any]]:
        """检测SQL中的字段冲突"""
        conflicts = []
        
        for conflict_name, conflict_info in self.field_conflict_patterns.items():
            conflicting_fields = conflict_info["conflicting_fields"]
            
            # 检查SQL中是否同时使用了冲突字段
            used_fields = []
            for field in conflicting_fields:
                if re.search(rf'\b{re.escape(field)}\b', sql, re.IGNORECASE):
                    used_fields.append(field)
            
            if len(used_fields) > 1:
                conflicts.append({
                    "type": "字段冲突",
                    "conflict_name": conflict_name,
                    "description": conflict_info["conflict_description"],
                    "conflicting_fields": used_fields,
                    "preferred_field": conflict_info["preferred_field"],
                    "severity": "高"
                })
        
        return conflicts
    
    def _check_field_priority_violations(self, sql: str, query: str) -> List[Dict[str, Any]]:
        """检查字段优先级违规"""
        violations = []
        
        # 获取字段使用意图分析
        available_fields = self._extract_fields_from_sql(sql)
        field_preferences = query_intent_analyzer.get_preferred_fields(query, available_fields)
        
        # 检查是否使用了被排除的字段
        for excluded_field in field_preferences["excluded_fields"]:
            if re.search(rf'\b{re.escape(excluded_field)}\b', sql, re.IGNORECASE):
                reasoning = field_preferences["field_reasoning"].get(excluded_field, {})
                violations.append({
                    "type": "优先级违规",
                    "description": f"使用了被排除的字段: {excluded_field}",
                    "violated_field": excluded_field,
                    "reasoning": reasoning.get("reasoning", ["字段优先级规则"]),
                    "severity": "中"
                })
        
        return violations
    
    def _extract_fields_from_sql(self, sql: str) -> List[str]:
        """从SQL中提取字段名"""
        # 简单的字段提取逻辑，可以根据需要改进
        common_fields = [
            "account_code", "account_full_name", "account_name",
            "accounting_unit_name", "accounting_organization", "company_name",
            "debit_amount", "credit_amount", "balance", "year", "month"
        ]
        
        found_fields = []
        for field in common_fields:
            if re.search(rf'\b{re.escape(field)}\b', sql, re.IGNORECASE):
                found_fields.append(field)
        
        return found_fields
    
    def _generate_correction_suggestions(self, sql: str, query: str, violations: List[Dict[str, Any]]) -> List[str]:
        """生成修正建议"""
        suggestions = []
        
        for violation in violations:
            if violation["type"] == "字段冲突":
                preferred = violation["preferred_field"]
                conflicting = violation["conflicting_fields"]
                suggestions.append(
                    f"建议只使用 `{preferred}` 字段，移除 {', '.join(f'`{f}`' for f in conflicting if f != preferred)}"
                )
            
            elif violation["type"] == "优先级违规":
                violated_field = violation["violated_field"]
                suggestions.append(
                    f"建议移除字段 `{violated_field}`，使用更合适的替代字段"
                )
        
        return suggestions
    
    def _auto_correct_sql(self, sql: str, query: str, violations: List[Dict[str, Any]]) -> str:
        """自动修正SQL"""
        corrected_sql = sql
        
        # 获取字段偏好
        available_fields = self._extract_fields_from_sql(sql)
        field_preferences = query_intent_analyzer.get_preferred_fields(query, available_fields)
        
        # 应用字段替换
        for violation in violations:
            if violation["type"] == "字段冲突":
                preferred = violation["preferred_field"]
                conflicting = violation["conflicting_fields"]
                
                # 将所有冲突字段替换为首选字段
                for field in conflicting:
                    if field != preferred:
                        corrected_sql = re.sub(
                            rf'\b{re.escape(field)}\b',
                            preferred,
                            corrected_sql,
                            flags=re.IGNORECASE
                        )
            
            elif violation["type"] == "优先级违规":
                violated_field = violation["violated_field"]

                # 查找合适的替代字段
                replacement = self._find_replacement_field(violated_field, field_preferences)
                if replacement:
                    # 简单直接的替换，避免复杂的正则表达式
                    corrected_sql = re.sub(
                        rf'\b{re.escape(violated_field)}\b',
                        replacement,
                        corrected_sql,
                        flags=re.IGNORECASE
                    )
        
        return corrected_sql
    
    def _find_replacement_field(self, violated_field: str, field_preferences: Dict[str, Any]) -> str:
        """查找违规字段的替代字段"""
        
        # 科目字段替换规则
        if violated_field in ["account_code", "account_name"]:
            if "account_full_name" in field_preferences["preferred_fields"]:
                return "account_full_name"
        
        # 公司字段替换规则
        if violated_field in ["accounting_organization", "company_name"]:
            if "accounting_unit_name" in field_preferences["preferred_fields"]:
                return "accounting_unit_name"
        
        return None
    
    def get_validation_summary(self, validation_result: Dict[str, Any]) -> str:
        """获取验证结果摘要"""
        if validation_result["is_valid"]:
            return "✅ SQL字段使用验证通过"
        
        summary_parts = ["❌ SQL字段使用验证失败:"]
        
        for violation in validation_result["violations"]:
            severity_icon = "🔴" if violation["severity"] == "高" else "🟡"
            summary_parts.append(f"  {severity_icon} {violation['description']}")
        
        if validation_result["suggestions"]:
            summary_parts.append("\n💡 修正建议:")
            for suggestion in validation_result["suggestions"]:
                summary_parts.append(f"  - {suggestion}")
        
        return "\n".join(summary_parts)

# 全局SQL字段验证器实例
sql_field_validator = SQLFieldValidator()
