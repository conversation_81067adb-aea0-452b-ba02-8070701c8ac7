"""
SQL格式标准化模块
确保科目筛选条件使用标准的 %XX费用% 格式
"""

import re
from typing import Dict, List, Tuple
from app.config.subject_keywords import get_subject_keywords, get_standard_filter_condition

class SQLFormatStandardizer:
    """SQL格式标准化器"""
    
    def __init__(self):
        self.subject_keywords = get_subject_keywords()
        self._build_standardization_patterns()
    
    def _build_standardization_patterns(self):
        """构建标准化模式"""
        self.standardization_patterns = []
        
        for standard_name, keywords in self.subject_keywords.items():
            # 为每个科目的所有关键词创建标准化模式
            for keyword in keywords:
                # 匹配各种可能的LIKE格式
                patterns = [
                    # account_full_name LIKE '管理费用%' (缺少前置%)
                    rf"account_full_name\s+LIKE\s+['\"]({re.escape(keyword)}%)['\"]",
                    # account_full_name LIKE '%管理费用' (缺少后置%)
                    rf"account_full_name\s+LIKE\s+['\"](%{re.escape(keyword)})['\"]",
                    # account_full_name LIKE '管理费用' (完全没有%)
                    rf"account_full_name\s+LIKE\s+['\"]({re.escape(keyword)})['\"]",
                    # account_full_name = '管理费用'
                    rf"account_full_name\s*=\s*['\"]({re.escape(keyword)})['\"]",
                ]
                
                standard_condition = get_standard_filter_condition(standard_name)
                
                for pattern in patterns:
                    self.standardization_patterns.append({
                        'pattern': pattern,
                        'standard_condition': standard_condition,
                        'keyword': keyword,
                        'standard_name': standard_name
                    })
    
    def standardize_subject_filters(self, sql: str) -> Tuple[str, List[str]]:
        """
        标准化SQL中的科目筛选条件
        
        Args:
            sql: 原始SQL
            
        Returns:
            Tuple[str, List[str]]: (标准化后的SQL, 修改记录)
        """
        standardized_sql = sql
        modifications = []
        
        for pattern_info in self.standardization_patterns:
            pattern = pattern_info['pattern']
            standard_condition = pattern_info['standard_condition']
            keyword = pattern_info['keyword']
            standard_name = pattern_info['standard_name']
            
            # 查找匹配的模式
            matches = re.finditer(pattern, standardized_sql, re.IGNORECASE)
            
            for match in matches:
                original_condition = match.group(0)
                
                # 替换为标准格式
                standardized_sql = standardized_sql.replace(
                    original_condition, 
                    standard_condition,
                    1  # 只替换第一个匹配
                )
                
                modifications.append(
                    f"'{original_condition}' → '{standard_condition}'"
                )
        
        return standardized_sql, modifications
    
    def validate_subject_filter_format(self, sql: str) -> Dict[str, any]:
        """
        验证SQL中科目筛选条件的格式
        
        Args:
            sql: SQL语句
            
        Returns:
            Dict: 验证结果
        """
        validation_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': []
        }
        
        # 检查是否有科目相关的LIKE条件
        like_patterns = re.finditer(
            r'account_full_name\s+LIKE\s+[\'"]([^\'\"]+)[\'"]',
            sql,
            re.IGNORECASE
        )
        
        for match in like_patterns:
            like_value = match.group(1)
            full_condition = match.group(0)
            
            # 检查格式是否标准
            if not (like_value.startswith('%') and like_value.endswith('%')):
                validation_result['is_valid'] = False
                validation_result['issues'].append({
                    'type': '格式不标准',
                    'condition': full_condition,
                    'issue': f"LIKE值 '{like_value}' 应该使用 '%XX费用%' 格式"
                })
                
                # 尝试生成建议
                inner_value = like_value.strip('%')
                suggested_value = f"'%{inner_value}%'"
                validation_result['suggestions'].append(
                    f"建议将 '{like_value}' 改为 {suggested_value}"
                )
        
        return validation_result
    
    def get_standardization_examples(self) -> List[Dict[str, str]]:
        """获取标准化示例"""
        examples = []
        
        common_subjects = ['管理费用', '销售费用', '财务费用', '研发费用']
        
        for subject in common_subjects:
            if subject in [name for name in self.subject_keywords.keys()]:
                standard_condition = get_standard_filter_condition(subject)
                
                examples.append({
                    'subject': subject,
                    'wrong_formats': [
                        f"account_full_name LIKE '{subject}%'",
                        f"account_full_name LIKE '%{subject}'",
                        f"account_full_name = '{subject}'"
                    ],
                    'correct_format': standard_condition
                })
        
        return examples
    
    def generate_format_guidance(self) -> str:
        """生成格式指导文本"""
        guidance = "### 🎯 科目筛选条件标准格式指导:\n\n"
        
        examples = self.get_standardization_examples()
        
        for example in examples[:3]:  # 只显示前3个例子
            guidance += f"**{example['subject']}查询**:\n"
            guidance += f"✅ 正确格式: `{example['correct_format']}`\n"
            guidance += f"❌ 错误格式:\n"
            for wrong in example['wrong_formats']:
                guidance += f"   - `{wrong}`\n"
            guidance += "\n"
        
        guidance += "🚨 **重要提醒**: 所有科目筛选必须使用 `account_full_name LIKE '%XX费用%'` 格式！\n"
        
        return guidance

# 全局SQL格式标准化器实例
sql_format_standardizer = SQLFormatStandardizer()
