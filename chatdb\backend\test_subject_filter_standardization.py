#!/usr/bin/env python3
"""
测试科目筛选条件格式标准化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sql_format_standardizer():
    """测试SQL格式标准化器"""
    print("🧪 测试SQL格式标准化器")
    print("=" * 60)
    
    try:
        from app.services.sql_format_standardizer import sql_format_standardizer
        
        # 测试用例：各种不标准的SQL格式
        test_cases = [
            {
                "name": "缺少前置%的格式",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '管理费用%'
                GROUP BY accounting_unit_name
                """,
                "expected_standard": "account_full_name LIKE '%管理费用%'"
            },
            {
                "name": "缺少后置%的格式",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '%销售费用'
                GROUP BY accounting_unit_name
                """,
                "expected_standard": "account_full_name LIKE '%销售费用%'"
            },
            {
                "name": "使用等号的格式",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name = '财务费用'
                GROUP BY accounting_unit_name
                """,
                "expected_standard": "account_full_name LIKE '%财务费用%'"
            },
            {
                "name": "完全没有%的格式",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '研发费用'
                GROUP BY accounting_unit_name
                """,
                "expected_standard": "account_full_name LIKE '%研发费用%'"
            },
            {
                "name": "已经标准的格式（不应修改）",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '%管理费用%'
                GROUP BY accounting_unit_name
                """,
                "expected_standard": "account_full_name LIKE '%管理费用%'"
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔸 测试用例 {i}: {case['name']}")
            print(f"  原始SQL: {case['sql'].strip()[:80]}...")
            
            # 应用标准化
            standardized_sql, modifications = sql_format_standardizer.standardize_subject_filters(case['sql'])
            
            print(f"  标准化结果:")
            if modifications:
                print(f"    修改数量: {len(modifications)}")
                for mod in modifications:
                    print(f"    - {mod}")
            else:
                print(f"    无需修改（已经标准）")
            
            # 验证结果
            expected = case['expected_standard']
            if expected in standardized_sql:
                print(f"    ✅ 包含期望的标准格式: {expected}")
                success_count += 1
            else:
                print(f"    ❌ 未找到期望的标准格式: {expected}")
                print(f"    实际结果: {standardized_sql.strip()[:100]}...")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试SQL格式标准化器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_validation():
    """测试格式验证功能"""
    print(f"\n🧪 测试格式验证功能")
    print("=" * 60)
    
    try:
        from app.services.sql_format_standardizer import sql_format_standardizer
        
        # 测试用例
        test_cases = [
            {
                "name": "标准格式（应该通过）",
                "sql": "SELECT * FROM financial_data WHERE account_full_name LIKE '%管理费用%'",
                "should_pass": True
            },
            {
                "name": "非标准格式1（应该失败）",
                "sql": "SELECT * FROM financial_data WHERE account_full_name LIKE '管理费用%'",
                "should_pass": False
            },
            {
                "name": "非标准格式2（应该失败）",
                "sql": "SELECT * FROM financial_data WHERE account_full_name LIKE '%管理费用'",
                "should_pass": False
            },
            {
                "name": "无科目筛选（应该通过）",
                "sql": "SELECT * FROM financial_data WHERE year = 2024",
                "should_pass": True
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔸 测试用例 {i}: {case['name']}")
            print(f"  SQL: {case['sql']}")
            
            # 验证格式
            validation_result = sql_format_standardizer.validate_subject_filter_format(case['sql'])
            
            print(f"  验证结果:")
            print(f"    是否有效: {'✅' if validation_result['is_valid'] else '❌'}")
            
            if validation_result['issues']:
                print(f"    发现问题:")
                for issue in validation_result['issues']:
                    print(f"      - {issue['type']}: {issue['issue']}")
            
            if validation_result['suggestions']:
                print(f"    修改建议:")
                for suggestion in validation_result['suggestions']:
                    print(f"      - {suggestion}")
            
            # 检查结果是否符合预期
            actual_valid = validation_result['is_valid']
            expected_valid = case['should_pass']
            
            if actual_valid == expected_valid:
                print(f"    ✅ 验证结果符合预期")
                success_count += 1
            else:
                print(f"    ❌ 验证结果不符合预期")
                print(f"      期望通过: {expected_valid}, 实际通过: {actual_valid}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试格式验证功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_subject_intent_with_standard_format():
    """测试科目意图检测的标准格式生成"""
    print(f"\n🧪 测试科目意图检测的标准格式生成")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import detect_subject_query_intent
        
        # 测试查询
        test_queries = [
            "分析2024年11月各公司的管理费用",
            "查询销售费用最高的部门",
            "统计财务费用情况",
            "研发费用明细报告"
        ]
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔸 测试查询 {i}: {query}")
            
            # 检测意图
            intent = detect_subject_query_intent(query)
            
            print(f"  检测结果:")
            print(f"    有科目查询: {'✅' if intent['has_subject_query'] else '❌'}")
            
            if intent['subjects']:
                for subject in intent['subjects']:
                    condition = subject['condition']
                    print(f"    科目: {subject['standard_name']}")
                    print(f"    条件: {condition}")
                    
                    # 验证条件格式
                    if "LIKE '%" in condition and condition.endswith("%'"):
                        print(f"    ✅ 条件格式标准")
                        success_count += 1
                    else:
                        print(f"    ❌ 条件格式不标准")
            else:
                print(f"    无科目检测")
        
        print(f"\n📊 测试结果: {success_count}/{len([q for q in test_queries])} 个查询生成标准格式")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试科目意图检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_standardization():
    """端到端标准化测试"""
    print(f"\n🧪 端到端标准化测试")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import apply_subject_filter_standardization
        
        # 测试SQL：包含多种不标准格式
        test_sql = """
        SELECT 
            accounting_unit_name AS 公司名称,
            SUM(debit_amount) AS 费用总额
        FROM financial_data 
        WHERE year = 2024 
            AND month = 11 
            AND account_full_name LIKE '管理费用%'
            AND (account_full_name = '销售费用' OR account_full_name LIKE '%财务费用')
        GROUP BY accounting_unit_name
        ORDER BY 费用总额 DESC
        """
        
        print(f"📋 测试SQL（包含多种不标准格式）:")
        print(test_sql.strip())
        
        # 应用标准化
        standardized_sql = apply_subject_filter_standardization(test_sql)
        
        print(f"\n📄 标准化后的SQL:")
        print(standardized_sql.strip())
        
        # 验证标准化效果
        expected_patterns = [
            "account_full_name LIKE '%管理费用%'",
            "account_full_name LIKE '%销售费用%'", 
            "account_full_name LIKE '%财务费用%'"
        ]
        
        success_count = 0
        print(f"\n✅ 标准化效果验证:")
        
        for pattern in expected_patterns:
            if pattern in standardized_sql:
                print(f"  ✅ 包含标准格式: {pattern}")
                success_count += 1
            else:
                print(f"  ❌ 缺少标准格式: {pattern}")
        
        print(f"\n📊 标准化效果: {success_count}/{len(expected_patterns)} 个格式正确")
        return success_count == len(expected_patterns)
        
    except Exception as e:
        print(f"❌ 端到端标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 科目筛选条件格式标准化测试")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("SQL格式标准化器", test_sql_format_standardizer),
        ("格式验证功能", test_format_validation),
        ("科目意图标准格式", test_subject_intent_with_standard_format),
        ("端到端标准化", test_end_to_end_standardization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 科目筛选格式标准化测试结果")
    print("=" * 80)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！科目筛选格式标准化系统工作正常")
        print("\n💡 标准化效果:")
        print("1. ✅ 自动检测并修正不标准的LIKE格式")
        print("2. ✅ 统一使用 account_full_name LIKE '%XX费用%' 格式")
        print("3. ✅ 科目意图检测生成标准格式条件")
        print("4. ✅ SQL后处理自动应用格式标准化")
        print("\n🔄 请重启服务以应用所有优化")
    else:
        print("\n⚠️ 部分测试失败，请检查相关模块")

if __name__ == "__main__":
    main()
