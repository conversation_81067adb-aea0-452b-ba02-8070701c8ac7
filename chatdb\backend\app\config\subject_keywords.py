"""
科目关键词映射配置
用于Text2SQL系统的科目查询意图识别
"""

# 科目关键词映射配置
# 格式: '标准科目名称': ['关键词1', '关键词2', '关键词3', ...]
SUBJECT_KEYWORDS = {
    # 费用类科目
    '管理费用': [
        '管理费用', '管理费', '管理成本', '管理支出',
        '行政费用'
    ],
    
    '销售费用': [
        '销售费用', '销售费', '销售成本', '销售支出',
        '营销费用', '营销成本', '市场费用', '推广费用'
    ],
    
    '财务费用': [
        '财务费用', '财务费'
    ],
    
    '研发费用': [
        '研发费用', '研发费', '研发成本', '研发支出',
        '开发费用'
    ],
    
    # 收入类科目
    '营业收入': [
        '营业收入', '营业收益', '收入', '销售收入',
        '主营业务收入', '业务收入', '经营收入'
    ],
    
    '其他业务收入': [
        '其他业务收入', '其他收入', '非主营收入'
    ],
    
    # 成本类科目
    '主营业务成本': [
        '主营业务成本', '营业成本', '成本', '直接成本',
        '生产成本', '制造成本', '产品成本'
    ],
    
    '其他业务成本': [
        '其他业务成本', '其他成本'
    ],
    
    # 税费类科目
    '税金及附加': [
        '税金及附加', '税费', '各种税费', '附加税',
        '营业税金', '税收支出'
    ],
    
    # 投资类科目
    '投资收益': [
        '投资收益', '投资收入', '股权投资收益', '理财收益'
    ],
    
    # 资产类科目
    '固定资产': [
        '固定资产'
    ],
    
    '无形资产': [
        '无形资产'
    ],
    
    # 负债类科目
    '应付账款': [
        '应付账款', '应付款', '欠款', '供应商欠款'
    ],
    
    '短期借款': [
        '短期借款', '短期贷款', '流动负债',
        '银行借款', '临时借款'
    ],
    
    '长期借款': [
        '长期借款', '长期贷款', '长期负债',
        '银行贷款', '长期融资'
    ],

    '银行存款': [
        '资金余额', '货币资金', '银行存款', '资金'
    ]
}

# 科目分类配置（可选，用于更复杂的查询分析）
SUBJECT_CATEGORIES = {
    '费用': ['管理费用', '销售费用', '财务费用', '研发费用'],
    '收入': ['营业收入', '其他业务收入', '投资收益'],
    '成本': ['主营业务成本', '其他业务成本'],
    '资产': ['固定资产', '无形资产'],
    '负债': ['应付账款', '短期借款', '长期借款'],
    '税费': ['税金及附加']
}

# 科目查询模式配置
SUBJECT_QUERY_PATTERNS = {
    # 金额相关字段映射
    '费用': 'debit_amount',    # 费用通常在借方
    '成本': 'debit_amount',    # 成本通常在借方
    '收入': 'credit_amount',   # 收入通常在贷方
    '资产': 'debit_amount',    # 资产增加在借方
    '负债': 'credit_amount',   # 负债增加在贷方
}

# 科目筛选条件标准格式配置
SUBJECT_FILTER_PATTERNS = {
    # 标准科目名称 → 标准筛选条件格式
    '管理费用': "account_full_name LIKE '%管理费用%'",
    '销售费用': "account_full_name LIKE '%销售费用%'",
    '财务费用': "account_full_name LIKE '%财务费用%'",
    '研发费用': "account_full_name LIKE '%研发费用%'",
    '营业收入': "account_full_name LIKE '%营业收入%'",
    '其他业务收入': "account_full_name LIKE '%其他业务收入%'",
    '主营业务成本': "account_full_name LIKE '%主营业务成本%'",
    '其他业务成本': "account_full_name LIKE '%其他业务成本%'",
    '税金及附加': "account_full_name LIKE '%税金及附加%'",
    '投资收益': "account_full_name LIKE '%投资收益%'",
    '固定资产': "account_full_name LIKE '%固定资产%'",
    '无形资产': "account_full_name LIKE '%无形资产%'",
    '应付账款': "account_full_name LIKE '%应付账款%'",
    '短期借款': "account_full_name LIKE '%短期借款%'",
    '长期借款': "account_full_name LIKE '%长期借款%'"
}

def get_subject_keywords():
    """获取科目关键词映射"""
    return SUBJECT_KEYWORDS

def get_subject_categories():
    """获取科目分类配置"""
    return SUBJECT_CATEGORIES

def get_subject_query_patterns():
    """获取科目查询模式配置"""
    return SUBJECT_QUERY_PATTERNS

def get_subject_filter_patterns():
    """获取科目筛选条件标准格式配置"""
    return SUBJECT_FILTER_PATTERNS

def get_standard_filter_condition(subject_name: str) -> str:
    """
    获取科目的标准筛选条件

    Args:
        subject_name: 科目名称

    Returns:
        str: 标准筛选条件，如 "account_full_name LIKE '%管理费用%'"
    """
    return SUBJECT_FILTER_PATTERNS.get(subject_name, f"account_full_name LIKE '%{subject_name}%'")

def add_custom_subject(standard_name: str, keywords: list, category: str = None):
    """
    动态添加自定义科目
    
    Args:
        standard_name: 标准科目名称
        keywords: 关键词列表
        category: 科目分类（可选）
    """
    SUBJECT_KEYWORDS[standard_name] = keywords
    
    if category and category in SUBJECT_CATEGORIES:
        SUBJECT_CATEGORIES[category].append(standard_name)

def get_subject_by_keyword(keyword: str):
    """
    根据关键词查找对应的标准科目名称
    
    Args:
        keyword: 查询关键词
        
    Returns:
        str: 标准科目名称，如果未找到返回None
    """
    for standard_name, keywords in SUBJECT_KEYWORDS.items():
        if keyword in keywords:
            return standard_name
    return None
