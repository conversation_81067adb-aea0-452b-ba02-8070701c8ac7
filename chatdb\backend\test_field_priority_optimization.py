#!/usr/bin/env python3
"""
测试科目字段优化系统
验证字段优先级规则、意图识别和SQL验证功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_query_intent_analyzer():
    """测试查询意图分析器"""
    print("🧪 测试查询意图分析器")
    print("=" * 60)
    
    try:
        from app.services.query_intent_analyzer import query_intent_analyzer
        
        # 测试用例
        test_cases = [
            {
                "name": "默认科目查询（应优先使用account_full_name）",
                "query": "分析2024年11月各公司的管理费用",
                "available_fields": ["account_code", "account_full_name", "account_name"],
                "expected_preferred": "account_full_name",
                "expected_excluded": ["account_code", "account_name"]
            },
            {
                "name": "明确要求科目代码（应使用account_code）",
                "query": "查询科目代码为1001的记录",
                "available_fields": ["account_code", "account_full_name", "account_name"],
                "expected_preferred": "account_code",
                "expected_excluded": ["account_full_name"]
            },
            {
                "name": "科目名称查询（应优先使用account_full_name）",
                "query": "查询科目名称包含管理费用的数据",
                "available_fields": ["account_code", "account_full_name", "account_name"],
                "expected_preferred": "account_full_name",
                "expected_excluded": ["account_code", "account_name"]
            },
            {
                "name": "公司字段优先级测试",
                "query": "各公司的销售情况",
                "available_fields": ["accounting_unit_name", "accounting_organization"],
                "expected_preferred": "accounting_unit_name",
                "expected_excluded": ["accounting_organization"]
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔸 测试用例 {i}: {case['name']}")
            print(f"  查询: {case['query']}")
            print(f"  可用字段: {case['available_fields']}")
            
            # 分析字段偏好
            preferences = query_intent_analyzer.get_preferred_fields(
                case['query'], case['available_fields']
            )
            
            print(f"  分析结果:")
            print(f"    首选字段: {preferences['preferred_fields']}")
            print(f"    排除字段: {preferences['excluded_fields']}")
            
            # 验证结果
            expected_preferred = case['expected_preferred']
            expected_excluded = case.get('expected_excluded', [])
            
            preferred_correct = expected_preferred in preferences['preferred_fields']
            excluded_correct = all(field in preferences['excluded_fields'] for field in expected_excluded)
            
            if preferred_correct and excluded_correct:
                print(f"    ✅ 测试通过")
                success_count += 1
            else:
                print(f"    ❌ 测试失败")
                if not preferred_correct:
                    print(f"      期望首选: {expected_preferred}, 实际: {preferences['preferred_fields']}")
                if not excluded_correct:
                    print(f"      期望排除: {expected_excluded}, 实际: {preferences['excluded_fields']}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试查询意图分析器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sql_field_validator():
    """测试SQL字段验证器"""
    print(f"\n🧪 测试SQL字段验证器")
    print("=" * 60)
    
    try:
        from app.services.sql_field_validator import sql_field_validator
        
        # 测试用例
        test_cases = [
            {
                "name": "字段冲突检测（同时使用account_code和account_full_name）",
                "sql": """
                SELECT accounting_unit_name, account_code, account_full_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_code = '1001' AND account_full_name LIKE '%管理费用%'
                GROUP BY accounting_unit_name, account_code, account_full_name
                """,
                "query": "查询管理费用明细",
                "should_fail": True,
                "expected_violations": ["字段冲突"]
            },
            {
                "name": "优先级违规检测（使用被排除的字段）",
                "sql": """
                SELECT accounting_organization, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '%管理费用%'
                GROUP BY accounting_organization
                """,
                "query": "各公司管理费用统计",
                "should_fail": True,
                "expected_violations": ["优先级违规"]
            },
            {
                "name": "正确的SQL（应该通过验证）",
                "sql": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data 
                WHERE account_full_name LIKE '%管理费用%'
                GROUP BY accounting_unit_name
                """,
                "query": "各公司管理费用统计",
                "should_fail": False,
                "expected_violations": []
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔸 测试用例 {i}: {case['name']}")
            print(f"  查询: {case['query']}")
            print(f"  SQL: {case['sql'].strip()[:100]}...")
            
            # 验证SQL
            validation_result = sql_field_validator.validate_sql_field_usage(
                case['sql'], case['query']
            )
            
            print(f"  验证结果:")
            print(f"    是否有效: {'❌' if not validation_result['is_valid'] else '✅'}")
            print(f"    违规数量: {len(validation_result['violations'])}")
            
            if validation_result['violations']:
                print(f"    违规类型: {[v['type'] for v in validation_result['violations']]}")
            
            # 检查结果是否符合预期
            actual_failed = not validation_result['is_valid']
            expected_failed = case['should_fail']
            
            if actual_failed == expected_failed:
                print(f"    ✅ 验证结果符合预期")
                success_count += 1
                
                # 如果有修正建议，显示
                if validation_result['suggestions']:
                    print(f"    💡 修正建议:")
                    for suggestion in validation_result['suggestions']:
                        print(f"      - {suggestion}")
                
                # 如果有修正后的SQL，显示
                if validation_result['corrected_sql'] != case['sql']:
                    print(f"    🔧 已自动修正SQL")
                    
            else:
                print(f"    ❌ 验证结果不符合预期")
                print(f"      期望失败: {expected_failed}, 实际失败: {actual_failed}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试SQL字段验证器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_retriever_integration():
    """测试Schema检索器的字段过滤集成"""
    print(f"\n🧪 测试Schema检索器字段过滤集成")
    print("=" * 60)
    
    try:
        from app.services.query_intent_analyzer import query_intent_analyzer
        
        # 模拟schema_context
        mock_schema_context = {
            "columns": [
                {"id": 225, "name": "accounting_organization", "table_name": "financial_data"},
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 227, "name": "account_code", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"},
                {"id": 229, "name": "account_name", "table_name": "financial_data"},
                {"id": 230, "name": "debit_amount", "table_name": "financial_data"}
            ]
        }
        
        # 测试查询
        test_query = "分析2024年11月各公司的管理费用"
        
        print(f"📋 测试查询: {test_query}")
        print(f"📊 原始字段数量: {len(mock_schema_context['columns'])}")
        
        # 获取字段偏好
        available_fields = [col['name'] for col in mock_schema_context['columns']]
        field_preferences = query_intent_analyzer.get_preferred_fields(test_query, available_fields)
        
        print(f"🎯 字段偏好分析:")
        print(f"  首选字段: {field_preferences['preferred_fields']}")
        print(f"  排除字段: {field_preferences['excluded_fields']}")
        
        # 模拟Schema检索器的过滤逻辑
        filtered_columns = []
        for col in mock_schema_context['columns']:
            field_name = col['name']
            if field_name not in field_preferences['excluded_fields']:
                filtered_columns.append(col)
        
        print(f"📊 过滤后字段数量: {len(filtered_columns)}")
        print(f"🔍 保留的字段:")
        for col in filtered_columns:
            print(f"    - {col['name']}")
        
        # 验证关键字段的处理
        key_field_tests = [
            ("accounting_unit_name", True, "公司字段首选"),
            ("accounting_organization", False, "公司字段次选，应被排除"),
            ("account_full_name", True, "科目字段首选"),
            ("account_code", False, "科目字段次选，应被排除"),
            ("account_name", False, "科目字段次选，应被排除")
        ]
        
        success_count = 0
        print(f"\n🔍 关键字段验证:")
        
        for field_name, should_keep, description in key_field_tests:
            is_kept = any(col['name'] == field_name for col in filtered_columns)
            
            if is_kept == should_keep:
                print(f"  ✅ {field_name}: {description}")
                success_count += 1
            else:
                print(f"  ❌ {field_name}: {description}")
                print(f"      期望保留: {should_keep}, 实际保留: {is_kept}")
        
        print(f"\n📊 关键字段验证结果: {success_count}/{len(key_field_tests)} 通过")
        return success_count == len(key_field_tests)
        
    except Exception as e:
        print(f"❌ 测试Schema检索器集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_optimization():
    """端到端优化测试"""
    print(f"\n🧪 端到端优化测试")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import validate_and_correct_sql_fields
        
        # 测试用例：包含字段冲突的SQL
        problematic_sql = """
        SELECT 
            accounting_organization AS 公司名称,
            account_code,
            account_full_name,
            SUM(debit_amount) AS 管理费用总额
        FROM financial_data 
        WHERE year = 2024 
            AND month = 11 
            AND account_code = '1001'
            AND account_full_name LIKE '%管理费用%'
        GROUP BY accounting_organization, account_code, account_full_name
        ORDER BY 管理费用总额 DESC
        """
        
        test_query = "分析2024年11月各公司的管理费用"
        
        print(f"📋 测试查询: {test_query}")
        print(f"📄 问题SQL (包含字段冲突):")
        print(problematic_sql.strip())
        
        # 应用验证和修正
        corrected_sql, validation_result = validate_and_correct_sql_fields(
            problematic_sql, test_query
        )
        
        print(f"\n🔧 验证和修正结果:")
        print(f"  原SQL有效: {'❌' if not validation_result['is_valid'] else '✅'}")
        print(f"  违规数量: {len(validation_result['violations'])}")
        
        if validation_result['violations']:
            print(f"  检测到的问题:")
            for violation in validation_result['violations']:
                print(f"    - {violation['type']}: {violation['description']}")
        
        if validation_result['suggestions']:
            print(f"  修正建议:")
            for suggestion in validation_result['suggestions']:
                print(f"    - {suggestion}")
        
        print(f"\n📄 修正后的SQL:")
        print(corrected_sql.strip())
        
        # 验证修正效果
        expected_improvements = [
            ("使用accounting_unit_name而不是accounting_organization", 
             "accounting_unit_name" in corrected_sql and "accounting_organization" not in corrected_sql),
            ("移除account_code字段冲突", 
             corrected_sql.count("account_code") < problematic_sql.count("account_code")),
            ("保留account_full_name作为主要科目字段",
             "account_full_name" in corrected_sql)
        ]
        
        success_count = 0
        print(f"\n✅ 修正效果验证:")
        
        for improvement, condition in expected_improvements:
            if condition:
                print(f"  ✅ {improvement}")
                success_count += 1
            else:
                print(f"  ❌ {improvement}")
        
        print(f"\n📊 修正效果: {success_count}/{len(expected_improvements)} 项改进成功")
        return success_count == len(expected_improvements)
        
    except Exception as e:
        print(f"❌ 端到端优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 科目字段优化系统测试")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("查询意图分析器", test_query_intent_analyzer),
        ("SQL字段验证器", test_sql_field_validator),
        ("Schema检索器集成", test_schema_retriever_integration),
        ("端到端优化", test_end_to_end_optimization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 科目字段优化系统测试结果")
    print("=" * 80)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！科目字段优化系统工作正常")
        print("\n💡 系统优化效果:")
        print("1. ✅ 智能字段优先级选择")
        print("2. ✅ 科目字段冲突检测和修正")
        print("3. ✅ Schema检索阶段字段过滤")
        print("4. ✅ SQL生成后自动验证和修正")
        print("\n🔄 请重启服务以应用所有优化")
    else:
        print("\n⚠️ 部分测试失败，请检查相关模块")

if __name__ == "__main__":
    main()
