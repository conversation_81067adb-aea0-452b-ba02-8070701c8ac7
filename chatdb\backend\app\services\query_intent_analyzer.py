"""
查询意图分析模块
用于分析用户查询中的字段使用意图，特别是科目字段的优先级选择
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class FieldPriorityRule:
    """字段优先级规则"""
    field_group: str  # 字段组名（如'科目字段'）
    primary_field: str  # 主要字段
    secondary_fields: List[str]  # 次要字段列表
    priority_keywords: List[str]  # 触发优先级的关键词
    exclusion_keywords: List[str]  # 排除使用主要字段的关键词

class QueryIntentAnalyzer:
    """查询意图分析器"""
    
    def __init__(self):
        self.field_priority_rules = self._initialize_priority_rules()
    
    def _initialize_priority_rules(self) -> List[FieldPriorityRule]:
        """初始化字段优先级规则"""
        return [
            # 科目字段优先级规则
            FieldPriorityRule(
                field_group="科目字段",
                primary_field="account_full_name",
                secondary_fields=["account_code", "account_name"],
                priority_keywords=[
                    "科目名称", "科目全称", "会计科目名称", "科目描述",
                    "科目内容", "包含", "类似", "相关", "涉及"
                ],
                exclusion_keywords=[
                    "代码", "编码", "编号", "code", "编制", "代号",
                    "科目代码", "会计科目代码", "科目编号"
                ]
            ),
            
            # 公司字段优先级规则
            FieldPriorityRule(
                field_group="公司字段",
                primary_field="accounting_unit_name",
                secondary_fields=["accounting_organization", "company_name"],
                priority_keywords=[
                    "公司", "企业", "单位", "机构", "组织"
                ],
                exclusion_keywords=[]
            )
        ]
    
    def analyze_field_usage_intent(self, query: str) -> Dict[str, Any]:
        """
        分析查询中的字段使用意图
        
        Args:
            query: 用户查询字符串
            
        Returns:
            Dict: 字段使用意图分析结果
        """
        result = {
            "field_preferences": {},  # 字段偏好
            "exclusions": {},         # 排除的字段
            "reasoning": []           # 推理过程
        }
        
        for rule in self.field_priority_rules:
            preference = self._analyze_single_field_group(query, rule)
            if preference:
                result["field_preferences"][rule.field_group] = preference
                result["reasoning"].extend(preference.get("reasoning", []))
        
        return result
    
    def _analyze_single_field_group(self, query: str, rule: FieldPriorityRule) -> Optional[Dict[str, Any]]:
        """分析单个字段组的使用意图"""
        
        # 检查是否有排除关键词（明确要求使用次要字段）
        exclusion_found = any(keyword in query for keyword in rule.exclusion_keywords)
        
        # 检查是否有优先级关键词（明确要求使用主要字段）
        priority_found = any(keyword in query for keyword in rule.priority_keywords)
        
        reasoning = []
        
        if exclusion_found:
            # 用户明确要求使用代码等次要字段
            matched_exclusions = [kw for kw in rule.exclusion_keywords if kw in query]
            reasoning.append(f"检测到关键词 {matched_exclusions}，优先使用次要字段")
            
            return {
                "preferred_field": rule.secondary_fields[0] if rule.secondary_fields else rule.primary_field,
                "excluded_fields": [rule.primary_field],
                "confidence": 0.9,
                "reasoning": reasoning
            }
        
        elif priority_found:
            # 用户明确要求使用名称等主要字段
            matched_priorities = [kw for kw in rule.priority_keywords if kw in query]
            reasoning.append(f"检测到关键词 {matched_priorities}，优先使用主要字段")
            
            return {
                "preferred_field": rule.primary_field,
                "excluded_fields": rule.secondary_fields,
                "confidence": 0.9,
                "reasoning": reasoning
            }
        
        else:
            # 默认情况：使用主要字段
            reasoning.append(f"未检测到特殊关键词，使用默认优先级（主要字段）")
            
            return {
                "preferred_field": rule.primary_field,
                "excluded_fields": rule.secondary_fields,
                "confidence": 0.7,
                "reasoning": reasoning
            }
    
    def get_preferred_fields(self, query: str, available_fields: List[str]) -> Dict[str, Any]:
        """
        获取查询的首选字段列表
        
        Args:
            query: 用户查询
            available_fields: 可用字段列表
            
        Returns:
            Dict: 包含首选字段和排除字段的结果
        """
        intent_analysis = self.analyze_field_usage_intent(query)
        
        preferred_fields = []
        excluded_fields = []
        field_reasoning = {}
        
        # 处理每个字段组的偏好
        for field_group, preference in intent_analysis["field_preferences"].items():
            preferred_field = preference["preferred_field"]
            excluded_list = preference["excluded_fields"]
            
            # 只添加实际可用的字段
            if preferred_field in available_fields:
                preferred_fields.append(preferred_field)
                field_reasoning[preferred_field] = {
                    "group": field_group,
                    "confidence": preference["confidence"],
                    "reasoning": preference["reasoning"]
                }
            
            # 记录被排除的字段
            for excluded_field in excluded_list:
                if excluded_field in available_fields:
                    excluded_fields.append(excluded_field)
        
        # 添加其他未被处理的可用字段
        for field in available_fields:
            if field not in preferred_fields and field not in excluded_fields:
                preferred_fields.append(field)
                field_reasoning[field] = {
                    "group": "其他",
                    "confidence": 0.5,
                    "reasoning": ["未匹配特定规则，保持可用"]
                }
        
        return {
            "preferred_fields": preferred_fields,
            "excluded_fields": excluded_fields,
            "field_reasoning": field_reasoning,
            "analysis_summary": intent_analysis["reasoning"]
        }
    
    def should_exclude_field(self, query: str, field_name: str) -> bool:
        """
        判断是否应该排除某个字段
        
        Args:
            query: 用户查询
            field_name: 字段名
            
        Returns:
            bool: 是否应该排除
        """
        intent_analysis = self.analyze_field_usage_intent(query)
        
        for field_group, preference in intent_analysis["field_preferences"].items():
            if field_name in preference.get("excluded_fields", []):
                return True
        
        return False
    
    def get_field_selection_guidance(self, query: str) -> str:
        """
        获取字段选择指导文本
        
        Args:
            query: 用户查询
            
        Returns:
            str: 字段选择指导文本
        """
        intent_analysis = self.analyze_field_usage_intent(query)
        
        guidance_parts = []
        
        for field_group, preference in intent_analysis["field_preferences"].items():
            preferred = preference["preferred_field"]
            excluded = preference.get("excluded_fields", [])
            confidence = preference.get("confidence", 0.5)
            
            if confidence > 0.8:
                guidance_parts.append(
                    f"**{field_group}字段选择**: 强烈建议使用 `{preferred}` 字段"
                )
                if excluded:
                    guidance_parts.append(
                        f"  - 避免使用: {', '.join(f'`{field}`' for field in excluded)}"
                    )
            else:
                guidance_parts.append(
                    f"**{field_group}字段选择**: 建议优先使用 `{preferred}` 字段"
                )
        
        if guidance_parts:
            return "\n".join([
                "### 🎯 字段选择指导:",
                *guidance_parts,
                "\n⚠️ **重要**: 请严格按照上述字段选择建议生成SQL查询"
            ])
        
        return ""

# 全局查询意图分析器实例
query_intent_analyzer = QueryIntentAnalyzer()
